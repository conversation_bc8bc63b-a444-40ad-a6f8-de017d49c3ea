{"name": "Models Deployment", "nodes": [{"parameters": {}, "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 300], "webhookId": "deploy-models"}, {"parameters": {"command": "python enhanced_models_uploader.py"}, "name": "Upload Models", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"command": "gcloud app deploy --quiet"}, "name": "Deploy App", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [650, 300]}], "connections": {"Webhook": {"main": [[{"node": "Upload Models", "type": "main", "index": 0}]]}, "Upload Models": {"main": [[{"node": "Deploy App", "type": "main", "index": 0}]]}}}