FROM n8nio/n8n:latest

# تثبيت المتطلبات الإضافية
USER root
RUN apk add --no-cache     python3     py3-pip     curl     git     bash

# إعداد متغيرات البيئة
ENV N8N_HOST=0.0.0.0
ENV N8N_PORT=5678
ENV N8N_PROTOCOL=http
ENV NODE_ENV=production
ENV WEBHOOK_URL=https://n8n-automation-dot-universal-ai-assistants-2025.uc.r.appspot.com/

# إنشاء مجلد البيانات
RUN mkdir -p /home/<USER>/.n8n
RUN chown -R node:node /home/<USER>/.n8n

# العودة إلى مستخدم node
USER node

# تعيين مجلد العمل
WORKDIR /home/<USER>

# تشغيل n8n
CMD ["n8n", "start"]
