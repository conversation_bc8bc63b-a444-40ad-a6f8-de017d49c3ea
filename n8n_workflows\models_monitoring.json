{"name": "Models Monitoring", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "value": 1}]}}, "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"command": "gsutil ls -l gs://universal-ai-models-2025-storage/"}, "name": "Check Models", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"url": "https://universal-ai-assistants-2025.uc.r.appspot.com/api/models/status", "options": {}}, "name": "Update Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [650, 300]}], "connections": {"Schedule Trigger": {"main": [[{"node": "Check Models", "type": "main", "index": 0}]]}, "Check Models": {"main": [[{"node": "Update Status", "type": "main", "index": 0}]]}}}