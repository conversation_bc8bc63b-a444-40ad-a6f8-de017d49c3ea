#!/usr/bin/env python3
"""
نظام فحص وتحقق من النماذج المخزنة في Google Cloud Storage
Models Storage Verification System
"""

import os
import json
import subprocess
import time
from datetime import datetime
from pathlib import Path
import requests

class ModelsStorageVerifier:
    def __init__(self):
        self.project_id = "universal-ai-assistants-2025"
        self.bucket_name = "universal-ai-models-2025-storage"
        self.test_id = "1f3a1b34-73f7-4cd0-b273-04aa1af82775"
        self.report = {
            "test_id": self.test_id,
            "timestamp": datetime.now().isoformat(),
            "local_models": {},
            "cloud_models": {},
            "verification_results": {},
            "performance_metrics": {},
            "recommendations": []
        }
    
    def check_local_models(self):
        """فحص النماذج المحلية المتاحة"""
        print("🔍 فحص النماذج المحلية...")
        
        try:
            # فحص نماذج Ollama
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # تجاهل العنوان
                local_models = {}
                
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 3:
                            name = parts[0]
                            model_id = parts[1] if len(parts) > 1 else "unknown"
                            size = parts[2] if len(parts) > 2 else "unknown"
                            modified = " ".join(parts[3:]) if len(parts) > 3 else "unknown"
                            
                            local_models[name] = {
                                "id": model_id,
                                "size": size,
                                "modified": modified,
                                "status": "available"
                            }
                
                self.report["local_models"] = local_models
                print(f"✅ تم العثور على {len(local_models)} نموذج محلي")
                
                for name, info in local_models.items():
                    print(f"   📦 {name}: {info['size']}")
                    
            else:
                print("❌ خطأ في فحص نماذج Ollama")
                self.report["local_models"]["error"] = result.stderr
                
        except Exception as e:
            print(f"❌ خطأ في فحص النماذج المحلية: {e}")
            self.report["local_models"]["error"] = str(e)
    
    def check_cloud_storage(self):
        """فحص النماذج في Google Cloud Storage"""
        print("☁️ فحص النماذج في Google Cloud Storage...")
        
        try:
            # فحص Google Cloud Storage
            cmd = f"gsutil ls -l gs://{self.bucket_name}/"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                cloud_models = {}
                total_size = 0
                
                for line in lines:
                    if line.strip() and not line.startswith('TOTAL'):
                        parts = line.split()
                        if len(parts) >= 3 and parts[0].isdigit():
                            size = int(parts[0])
                            date_time = f"{parts[1]} {parts[2]}"
                            file_path = parts[3]
                            file_name = os.path.basename(file_path)
                            
                            if file_name.endswith('.bin') or 'model' in file_name.lower():
                                cloud_models[file_name] = {
                                    "size_bytes": size,
                                    "size_human": self.format_size(size),
                                    "uploaded": date_time,
                                    "path": file_path,
                                    "status": "uploaded"
                                }
                                total_size += size
                
                self.report["cloud_models"] = {
                    "models": cloud_models,
                    "total_count": len(cloud_models),
                    "total_size_bytes": total_size,
                    "total_size_human": self.format_size(total_size),
                    "bucket": self.bucket_name
                }
                
                print(f"✅ تم العثور على {len(cloud_models)} نموذج في Cloud Storage")
                print(f"📊 الحجم الإجمالي: {self.format_size(total_size)}")
                
                for name, info in cloud_models.items():
                    print(f"   ☁️ {name}: {info['size_human']}")
                    
            else:
                print("❌ خطأ في الوصول إلى Google Cloud Storage")
                print(f"Error: {result.stderr}")
                self.report["cloud_models"]["error"] = result.stderr
                
        except Exception as e:
            print(f"❌ خطأ في فحص Cloud Storage: {e}")
            self.report["cloud_models"]["error"] = str(e)
    
    def verify_model_integrity(self):
        """التحقق من سلامة النماذج"""
        print("🔐 التحقق من سلامة النماذج...")
        
        verification_results = {
            "local_verification": {},
            "cloud_verification": {},
            "sync_status": {}
        }
        
        # التحقق من النماذج المحلية
        if "models" not in self.report["local_models"]:
            local_models = self.report["local_models"]
        else:
            local_models = self.report["local_models"]["models"]
            
        for model_name in local_models:
            try:
                # اختبار تشغيل النموذج
                test_cmd = f"ollama run {model_name} 'test'"
                result = subprocess.run(test_cmd, shell=True, capture_output=True, 
                                      text=True, timeout=30)
                
                verification_results["local_verification"][model_name] = {
                    "status": "working" if result.returncode == 0 else "error",
                    "test_response": result.stdout[:100] if result.stdout else "no_response",
                    "error": result.stderr if result.stderr else None
                }
                
            except Exception as e:
                verification_results["local_verification"][model_name] = {
                    "status": "error",
                    "error": str(e)
                }
        
        # التحقق من مطابقة النماذج
        if "models" in self.report["cloud_models"]:
            cloud_models = self.report["cloud_models"]["models"]
            local_model_names = set(local_models.keys())
            cloud_model_names = set([name.replace('.bin', '').replace('_', ':') 
                                   for name in cloud_models.keys()])
            
            verification_results["sync_status"] = {
                "local_only": list(local_model_names - cloud_model_names),
                "cloud_only": list(cloud_model_names - local_model_names),
                "synced": list(local_model_names & cloud_model_names),
                "sync_percentage": len(local_model_names & cloud_model_names) / 
                                 max(len(local_model_names), 1) * 100
            }
        
        self.report["verification_results"] = verification_results
        
        # طباعة النتائج
        sync_status = verification_results["sync_status"]
        if sync_status:
            print(f"📊 حالة المزامنة: {sync_status['sync_percentage']:.1f}%")
            print(f"   ✅ متزامن: {len(sync_status['synced'])} نموذج")
            print(f"   🏠 محلي فقط: {len(sync_status['local_only'])} نموذج")
            print(f"   ☁️ سحابي فقط: {len(sync_status['cloud_only'])} نموذج")
    
    def test_cloud_download(self):
        """اختبار تحميل النماذج من Cloud Storage"""
        print("📥 اختبار تحميل النماذج من Cloud Storage...")
        
        download_tests = {}
        
        if "models" in self.report["cloud_models"]:
            cloud_models = self.report["cloud_models"]["models"]
            
            # اختبار تحميل أصغر نموذج
            if cloud_models:
                smallest_model = min(cloud_models.items(), 
                                    key=lambda x: x[1]["size_bytes"])
                
                model_name, model_info = smallest_model
                test_path = f"test_download_{model_name}"
                
                try:
                    start_time = time.time()
                    
                    # تحميل جزء صغير للاختبار (أول 1MB)
                    cmd = f"gsutil cp -r {model_info['path']} {test_path}"
                    result = subprocess.run(cmd, shell=True, capture_output=True, 
                                          text=True, timeout=120)
                    
                    download_time = time.time() - start_time
                    
                    if result.returncode == 0 and os.path.exists(test_path):
                        file_size = os.path.getsize(test_path)
                        download_speed = file_size / download_time if download_time > 0 else 0
                        
                        download_tests[model_name] = {
                            "status": "success",
                            "download_time": download_time,
                            "download_speed_mbps": download_speed / (1024*1024),
                            "file_size": file_size
                        }
                        
                        # حذف الملف المؤقت
                        os.remove(test_path)
                        
                        print(f"✅ تحميل {model_name}: {download_time:.2f}s")
                        print(f"   📊 السرعة: {download_speed/(1024*1024):.2f} MB/s")
                        
                    else:
                        download_tests[model_name] = {
                            "status": "failed",
                            "error": result.stderr
                        }
                        print(f"❌ فشل تحميل {model_name}")
                        
                except Exception as e:
                    download_tests[model_name] = {
                        "status": "error",
                        "error": str(e)
                    }
                    print(f"❌ خطأ في اختبار التحميل: {e}")
        
        self.report["performance_metrics"]["download_tests"] = download_tests
    
    def format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"

    def check_application_status(self):
        """فحص حالة التطبيق"""
        print("🚀 فحص حالة التطبيق...")

        app_status = {
            "local_app": {},
            "cloud_app": {},
            "api_endpoints": {}
        }

        # فحص التطبيق المحلي
        try:
            if os.path.exists("main.py"):
                app_status["local_app"]["main_py"] = "exists"

                # فحص محتوى main.py للتأكد من وجود وظائف النماذج السحابية
                with open("main.py", "r", encoding="utf-8") as f:
                    content = f.read()

                app_status["local_app"]["cloud_features"] = {
                    "cloud_models_manager": "cloud_models_manager" in content,
                    "gcs_integration": "google.cloud" in content,
                    "models_api": "/api/models" in content,
                    "download_endpoint": "download" in content
                }
            else:
                app_status["local_app"]["main_py"] = "missing"

        except Exception as e:
            app_status["local_app"]["error"] = str(e)

        # فحص ملفات Docker
        docker_files = ["Dockerfile", "Dockerfile.simple", "Dockerfile.cloud-optimized"]
        app_status["docker"] = {}

        for dockerfile in docker_files:
            app_status["docker"][dockerfile] = "exists" if os.path.exists(dockerfile) else "missing"

        # فحص cloud_models_manager.py
        if os.path.exists("cloud_models_manager.py"):
            app_status["cloud_manager"] = "exists"
        else:
            app_status["cloud_manager"] = "missing"

        self.report["application_status"] = app_status

        # طباعة النتائج
        print(f"📱 التطبيق المحلي: {'✅' if app_status['local_app'].get('main_py') == 'exists' else '❌'}")
        print(f"🐳 ملفات Docker: {sum(1 for v in app_status['docker'].values() if v == 'exists')}/3")
        print(f"☁️ مدير النماذج السحابية: {'✅' if app_status.get('cloud_manager') == 'exists' else '❌'}")

    def generate_recommendations(self):
        """توليد التوصيات"""
        recommendations = []

        # تحليل النتائج وتوليد التوصيات
        local_count = len(self.report.get("local_models", {}))
        cloud_count = self.report.get("cloud_models", {}).get("total_count", 0)

        if local_count > cloud_count:
            recommendations.append({
                "priority": "high",
                "category": "sync",
                "title": "رفع النماذج المحلية المتبقية",
                "description": f"يوجد {local_count - cloud_count} نموذج محلي لم يتم رفعه إلى Cloud Storage"
            })

        if cloud_count == 0:
            recommendations.append({
                "priority": "critical",
                "category": "storage",
                "title": "لا توجد نماذج في Cloud Storage",
                "description": "يجب رفع النماذج إلى Google Cloud Storage لتفعيل النظام السحابي"
            })

        # فحص حالة التطبيق
        app_status = self.report.get("application_status", {})
        if app_status.get("cloud_manager") == "missing":
            recommendations.append({
                "priority": "medium",
                "category": "application",
                "title": "إنشاء مدير النماذج السحابية",
                "description": "يجب إنشاء cloud_models_manager.py لإدارة النماذج السحابية"
            })

        # فحص الأداء
        download_tests = self.report.get("performance_metrics", {}).get("download_tests", {})
        if download_tests:
            avg_speed = sum(test.get("download_speed_mbps", 0)
                          for test in download_tests.values()) / len(download_tests)

            if avg_speed < 10:  # أقل من 10 MB/s
                recommendations.append({
                    "priority": "medium",
                    "category": "performance",
                    "title": "تحسين سرعة التحميل",
                    "description": f"سرعة التحميل الحالية {avg_speed:.1f} MB/s يمكن تحسينها"
                })

        self.report["recommendations"] = recommendations

        # طباعة التوصيات
        if recommendations:
            print("\n💡 التوصيات:")
            for rec in recommendations:
                priority_icon = {"critical": "🔴", "high": "🟠", "medium": "🟡"}.get(rec["priority"], "🔵")
                print(f"   {priority_icon} {rec['title']}")
                print(f"      {rec['description']}")

    def save_report(self):
        """حفظ التقرير"""
        report_file = f"models_verification_report_{self.test_id}.json"

        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(self.report, f, indent=2, ensure_ascii=False)

        print(f"\n📄 تم حفظ التقرير في: {report_file}")
        return report_file

    def run_comprehensive_test(self):
        """تشغيل الاختبار الشامل"""
        print("🧪 بدء الاختبار الشامل للنماذج المخزنة")
        print("=" * 60)
        print(f"🆔 معرف الاختبار: {self.test_id}")
        print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)

        # تشغيل جميع الاختبارات
        self.check_local_models()
        print()

        self.check_cloud_storage()
        print()

        self.verify_model_integrity()
        print()

        self.test_cloud_download()
        print()

        self.check_application_status()
        print()

        self.generate_recommendations()

        # حفظ التقرير
        report_file = self.save_report()

        # ملخص النتائج
        print("\n" + "=" * 60)
        print("📊 ملخص نتائج الاختبار")
        print("=" * 60)

        local_count = len(self.report.get("local_models", {}))
        cloud_count = self.report.get("cloud_models", {}).get("total_count", 0)
        cloud_size = self.report.get("cloud_models", {}).get("total_size_human", "0 B")

        print(f"🏠 النماذج المحلية: {local_count} نموذج")
        print(f"☁️ النماذج السحابية: {cloud_count} نموذج ({cloud_size})")

        sync_status = self.report.get("verification_results", {}).get("sync_status", {})
        if sync_status:
            print(f"🔄 نسبة المزامنة: {sync_status.get('sync_percentage', 0):.1f}%")

        recommendations_count = len(self.report.get("recommendations", []))
        print(f"💡 التوصيات: {recommendations_count} توصية")

        print(f"📄 التقرير المفصل: {report_file}")
        print("=" * 60)

        return self.report

if __name__ == "__main__":
    verifier = ModelsStorageVerifier()
    verifier.run_comprehensive_test()
